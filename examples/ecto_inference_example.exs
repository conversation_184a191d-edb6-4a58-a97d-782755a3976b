Application.put_env(:example_app, Example.Repo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000
)

Application.put_env(:example_app, :ecto_repos, [Example.Repo])

defmodule Example.Repo do
  use Ecto.Repo,
    otp_app: :example_app,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Example.User do
  use Ecto.Schema

  schema "users" do
    field(:name, :string)
    field(:email, :string)

    embeds_one(:address, Address) do
      field(:street, :string)
      field(:city, :string)
      field(:state, :string)
      field(:zip_code, :string)
      field(:country, :string)
    end

    timestamps()
  end
end

defmodule Example.App do
  use Drops.Operations
end

defmodule Example.CreateUser do
  use Example.App, :command

  schema(Example.User)

  @impl true
  def perform(params) do
    Example.Repo.insert(changeset(params))
  end
end

{:ok, _} = Application.ensure_all_started(:ecto_sql)
{:ok, _} = Example.Repo.start_link()

Ecto.Adapters.SQL.Sandbox.mode(Example.Repo, :manual)
:ok = Ecto.Adapters.SQL.Sandbox.checkout(Example.Repo)

Ecto.Adapters.SQL.query!(Example.Repo, """
  CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    email TEXT,
    address JSON,
    inserted_at DATETIME,
    updated_at DATETIME
  )
""")

valid_user_data = %{
  name: "John Doe",
  email: "<EMAIL>",
  address: %{
    street: "123 Main St",
    city: "Anytown",
    state: "CA",
    zip_code: "12345",
    country: "USA"
  }
}

case Example.CreateUser.execute(valid_user_data) do
  {:ok, %{result: result, params: params}} ->
    IO.inspect(result)
  _ ->
    IO.puts("✗ Operation failed")
end
