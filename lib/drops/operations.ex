defmodule Drops.Operations do
  @moduledoc """
  Operations module for defining command and query operations.

  This module provides a framework for defining operations that can be used
  to encapsulate business logic with input validation and execution.
  """

  defmodule Success do
    @type t :: %__MODULE__{}

    defstruct [:operation, :result, :params]
  end

  defmodule Failure do
    @type t :: %__MODULE__{}

    defstruct [:operation, :result, :params]
  end

  @doc """
  Callback for executing an operation with given parameters.
  """
  @callback perform(params :: any()) :: {:ok, any()} | {:error, any()}

  @callback validate(params :: any()) :: {:ok, any()} | {:error, any()}

  defmacro __using__(_opts) do
    quote do
      import Drops.Operations

      defmacro __using__(type) when is_atom(type) do
        quote do
          @behaviour Drops.Operations

          use Drops.Contract

          schema do
            %{}
          end

          def execute(params) do
            case validate(params) do
              {:ok, params} ->
                case perform(params) do
                  {:ok, result} ->
                    {:ok, %Success{operation: __MODULE__, result: result, params: params}}

                  {:error, error} ->
                    {:error,
                     %Failure{operation: __MODULE__, result: error, params: params}}
                end

              {:error, errors} ->
                {:error, %Failure{operation: __MODULE__, result: errors, params: params}}
            end
          end

          def perform(params) do
            raise "perform/1 must be implemented"
          end

          def validate(params) do
            if length(schema().keys) == 0, do: {:ok, params}, else: conform(params)
          end

          def changeset(params) do
            source_schema = schema().meta[:source_schema]

            Ecto.Changeset.change(struct(source_schema), params)
          end

          defoverridable perform: 1
          defoverridable validate: 1
        end
      end

      defmacro __using__(opts) when is_list(opts) do
        quote do
          @behaviour Drops.Operations

          use Drops.Contract

          # Store the repo configuration
          @repo unquote(opts[:repo])

          schema do
            %{}
          end

          def execute(params) do
            case validate(params) do
              {:ok, params} ->
                case perform(params) do
                  {:ok, result} ->
                    {:ok, %Success{operation: __MODULE__, result: result, params: params}}

                  {:error, error} ->
                    {:error,
                     %Failure{operation: __MODULE__, result: error, params: params}}
                end

              {:error, errors} ->
                {:error, %Failure{operation: __MODULE__, result: errors, params: params}}
            end
          end

          def perform(params) do
            raise "perform/1 must be implemented"
          end

          def validate(params) do
            if length(schema().keys) == 0, do: {:ok, params}, else: conform(params)
          end

          def changeset(params) do
            source_schema = schema().meta[:source_schema]

            Ecto.Changeset.change(struct(source_schema), params)
          end

          # Auto-defined create helper that uses configured repo
          def create(params) do
            if @repo do
              @repo.insert(changeset(params))
            else
              raise "No repo configured. Use `use MyApp, repo: MyRepo` to configure a repository."
            end
          end

          defoverridable perform: 1
          defoverridable validate: 1
        end
      end
    end
  end
end
