defmodule Drops.OperationsTest do
  use Drops.DataCase, async: true

  defmodule TestApp do
    use Drops.Operations
  end

  test "it works without schema" do
    defmodule CreateUser do
      use TestApp, :command

      @impl true
      def perform(params) do
        if params[:name] == nil do
          {:error, "name is required"}
        else
          {:ok, params}
        end
      end
    end

    {:ok, %{result: result, params: params}} = CreateUser.execute(%{name: "<PERSON>"})

    assert result == %{name: "<PERSON>"}
    assert params == %{name: "<PERSON>"}
  end

  test "it works with a schema" do
    defmodule CreateUserWithSchema do
      use TestApp, :command

      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def perform(params) do
        if params[:name] != "<PERSON>" do
          {:error, "name is not expected"}
        else
          {:ok, params}
        end
      end
    end

    {:ok, %{result: result, params: params}} =
      CreateUserWithSchema.execute(%{name: "<PERSON>"})

    assert result == %{name: "<PERSON>"}
    assert params == %{name: "<PERSON>"}

    {:error, %{result: result, params: params}} =
      CreateUserWithSchema.execute(%{name: ""})

    assert_errors(["name must be filled"], {:error, result})
    assert params == %{name: ""}
  end

  test "it works with an Ecto schema" do
    # Create the users table for this test
    Ecto.Adapters.SQL.query!(Drops.TestRepo, """
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT,
        inserted_at DATETIME,
        updated_at DATETIME
      )
    """)

    defmodule CreateUserWithEctoSchema do
      use TestApp, :command

      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case TestRepo.insert(changeset(params)) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    {:ok, %{result: result, params: params}} =
      CreateUserWithEctoSchema.execute(%{name: "Jane Doe"})

    assert result == %{name: "Jane Doe"}
    assert params == %{name: "Jane Doe"}
  end

  test "it works with an Ecto schema with embedded fields" do
    # Create the users_with_address table for this test
    Ecto.Adapters.SQL.query!(Drops.TestRepo, """
      CREATE TABLE users_with_address (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT,
        address JSON,
        inserted_at DATETIME,
        updated_at DATETIME
      )
    """)

    defmodule UserWithAddressSchema do
      @moduledoc "Test schema with embedded address"
      use Ecto.Schema
      import Ecto.Changeset

      schema "users_with_address" do
        field(:name, :string)
        field(:email, :string)

        embeds_one(:address, Address) do
          field(:street, :string)
          field(:city, :string)
          field(:state, :string)
          field(:zip_code, :string)
          field(:country, :string)
        end

        timestamps()
      end
    end

    defmodule CreateUserWithEmbeddedSchema do
      use(TestApp, :command, repo: Drops.TestRepo)

      schema(UserWithAddressSchema)

      @impl true
      def perform(params) do
        case create(params) do
          {:ok, user} -> {:ok, %{name: user.name, address: user.address}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    # Test with valid embedded data
    valid_params = %{
      name: "John Doe",
      email: "<EMAIL>",
      address: %{
        street: "123 Main St",
        city: "Anytown",
        state: "CA",
        zip_code: "12345",
        country: "USA"
      }
    }

    {:ok, %{result: result, params: params}} =
      CreateUserWithEmbeddedSchema.execute(valid_params)

    assert result.name == "John Doe"
    assert result.address.street == "123 Main St"
    assert result.address.city == "Anytown"
    assert params.name == "John Doe"
    assert params.address.street == "123 Main St"

    # Test with extra fields that should be filtered out
    params_with_extra = %{
      name: "Jane Smith",
      email: "<EMAIL>",
      address: %{
        street: "456 Oak Ave",
        city: "Springfield",
        state: "IL",
        zip_code: "62701"
      },
      age: 28,
      extra_field: "this should be ignored"
    }

    {:ok, %{result: result, params: params}} =
      CreateUserWithEmbeddedSchema.execute(params_with_extra)

    assert result.name == "Jane Smith"
    assert result.address.street == "456 Oak Ave"
    assert result.address.city == "Springfield"
    # Extra fields should be filtered out
    refute Map.has_key?(params, :age)
    refute Map.has_key?(params, :extra_field)
  end
end
